/* === SERVICE DETAIL === */
.service-detail {
	padding: 60px 0;
	background-color: #ffffff;
}
.service-detail .wrapper {
	display: flex;
	flex-direction: column;
	align-items: center;
}
.service-detail__title {
	font-size: 32px;
	font-weight: 600;
	color: #333333;
	text-align: center;
	margin-bottom: 40px;
}
.service-detail__content {
	display: flex;
	flex-direction: column;
	gap: 20px;
	width: 100%;
	max-width: 1000px;
}
.service-detail__image {
	width: 100%;
	height: auto;
	border-radius: 10px;
}
.service-detail__description {
	font-family: "Raleway", sans-serif;
	font-size: 16px;
	line-height: 1.6;
	color: rgba(51, 51, 51, 0.9);
}
.service-detail__button {
	background-color: #007bff;
	color: white;
	padding: 10px 30px;
	border-radius: 5px;
	font-weight: 500;
	font-size: 14px;
	text-decoration: none;
	transition: all 0.3s ease;
	align-self: center;
}
.service-detail__button:hover {
	background-color: #0056b3;
}

/* === SERVICE DETAIL HERO === */
.service-detail__hero {
	position: relative;
	min-height: 340px;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	border-radius: 0 0 32px 32px;
	overflow: hidden;
}
.bg-img {
	position: absolute;
	width: 100%;
	height: 100%;
	object-fit: cover;
	filter: brightness(50%); /* затемнення картинки */
	z-index: -1;
}
.service-detail__hero-title {
	font-size: 48px;
	font-weight: 700;
	margin-bottom: 16px;
	color: #333333;
	text-shadow: 0 2px 16px rgba(255, 255, 255, 0.8);
}
.service-detail__hero-subtitle {
	font-size: 20px;
	font-weight: 400;
	margin-bottom: 32px;
	color: #333333;
	text-shadow: 0 2px 16px rgba(255, 255, 255, 0.8);
}

/* === SERVICE DETAIL CONTENT === */
.service-detail__lead {
	font-size: 18px;
	font-weight: 500;
	margin-bottom: 24px;
}
.service-detail__features {
	display: flex;
	gap: 32px;
	margin: 32px 0;
	flex-wrap: wrap;
}
.feature {
	flex: 1 1 200px;
	background: rgba(51, 51, 51, 0.04);
	border-radius: 12px;
	padding: 20px;
	min-width: 220px;
}
.feature h3 {
	font-size: 20px;
	font-weight: 600;
	margin-bottom: 12px;
}
.feature ul {
	padding-left: 18px;
	margin: 0;
}
.feature li {
	font-size: 16px;
	margin-bottom: 6px;
}

/* === PRICING === */
.service-detail__pricing {
	margin: 32px 0;
}
.price-table {
	display: flex;
	flex-direction: column;
	gap: 10px;
	margin-bottom: 10px;
}
.price-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background: rgba(51, 51, 51, 0.06);
	border-radius: 8px;
	padding: 10px 18px;
	font-size: 16px;
	color: #333333;
}
.price-note {
	font-size: 13px;
	color: #aaa;
	margin-top: 8px;
}

/* === PROCESS === */
.service-detail__process {
	margin: 32px 0;
}
.service-detail__process h3 {
	font-size: 20px;
	font-weight: 600;
	margin-bottom: 12px;
}
.service-detail__process ol {
	padding-left: 20px;
}
.service-detail__process li {
	font-size: 16px;
	margin-bottom: 6px;
}

/* === CTA === */
.service-detail__cta {
	margin: 32px 0 0 0;
	text-align: center;
}

/* === FAQ === */
.service-detail__faq {
	margin: 48px 0 0 0;
}
.service-detail__faq h2 {
	font-size: 28px;
	font-weight: 700;
	margin-bottom: 24px;
	color: #333333;
	text-align: center;
}

/* === ACCORDION === */
.accordion {
	max-width: 1000px;
	margin: 0 auto;
}
.accordion__item {
	margin-bottom: 15px;
	border-bottom: 2px solid rgba(51, 51, 51, 0.3);
}
.accordion__button {
	width: 100%;
	padding: 20px;
	background: transparent;
	border: none;
	display: flex;
	justify-content: space-between;
	align-items: center;
	cursor: pointer;
	transition: all 0.3s ease;
}
.accordion__button span {
	font-size: 18px;
	color: #333333;
	text-align: left;
}
.accordion__button img {
	width: 24px;
	height: 24px;
	transition: transform 0.3s ease;
}
.accordion__button.active img {
	transform: rotate(180deg);
}
.accordion__content {
	max-height: 0;
	overflow: hidden;
	transition: max-height 0.3s ease;
	padding: 0 20px;
}
.accordion__content p {
	font-family: "Raleway", sans-serif;
	font-size: 16px;
	line-height: 1.6;
	color: rgba(51, 51, 51, 0.9);
	padding-bottom: 20px;
}
.accordion__button:hover {
	background: rgba(51, 51, 51, 0.05);
}
.accordion__item:hover {
	border-color: rgba(51, 51, 51, 0.4);
}

/* === MODAL === */
.modal {
	display: none;
	position: fixed;
	z-index: 2000;
	left: 0;
	top: 0;
	width: 100vw;
	height: 100vh;
	overflow: auto;
	background: rgba(0, 0, 0, 0.7);
	align-items: center;
	justify-content: center;
}
.modal.show {
	display: flex;
}
.modal-content {
	background: #fff;
	margin: 10% auto;
	padding: 30px 24px;
	border-radius: 10px;
	max-width: 400px;
	width: 100%;
	position: relative;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);
}
.close-modal {
	position: absolute;
	top: 12px;
	right: 18px;
	font-size: 28px;
	color: #007bff;
	cursor: pointer;
}

.form-group {
	margin-bottom: 20px;
}
.form-group label {
	display: block;
	margin-bottom: 5px;
	font-size: 14px;
	text-align: left;
}
.form-group input {
	width: 100%;
	padding: 10px;
	border: 1px solid #ddd;
	border-radius: 4px;
	font-size: 16px;
}
.form-group input:invalid {
	border-color: #007bff;
}
.modal-submit {
	width: 100%;
	padding: 12px;
	background-color: #007bff;
	color: white;
	border: none;
	border-radius: 4px;
	font-size: 16px;
	cursor: pointer;
	transition: background-color 0.3s;
}
.modal-submit:hover {
	background-color: #0056b3;
}

/* === MEDIA QUERIES === */
@media (max-width: 768px) {
	.service-detail__hero-title {
		font-size: 32px;
	}
	.service-detail__hero-content {
		padding: 40px 10px 24px 10px;
	}
	.service-detail__features {
		flex-direction: column;
		gap: 16px;
	}
	.feature {
		min-width: 0;
	}
	.modal-content {
		margin: 15% auto;
		width: 90%;
	}
}
@media (max-width: 576px) {
	.service-detail__hero-title {
		font-size: 22px;
	}
	.service-detail__hero-content {
		padding: 24px 4px 16px 4px;
	}
	.service-detail__hero-subtitle {
		font-size: 14px;
	}
	.service-detail__features {
		gap: 8px;
	}
	.feature {
		padding: 10px;
	}
	.service-detail__faq h2 {
		font-size: 20px;
	}
}

.check-list {
	list-style: none;
	padding: 0;
	margin: 0;
}
.check-list li {
	display: flex;
	align-items: flex-start;
	gap: 10px;
	font-size: 16px;
	margin-bottom: 10px;
	color: #333333;
	text-align: left;
}
.check-list li::before {
	content: "";
	display: inline-block;
	width: 18px;
	height: 18px;
	margin-top: 2px;
	background: url("./images/check-red.svg") no-repeat center/contain;
	flex-shrink: 0;
}
@media (max-width: 600px) {
	.check-list li {
		font-size: 15px;
		gap: 7px;
	}
	.check-list li::before {
		width: 15px;
		height: 15px;
	}
}
